import { UserType } from "../../mikerudenko/marketplace-shared";
import { refundAllActiveProposals } from "../../proposal-functions/proposal-service";
import {
  getUserData,
  requireAuthentication,
  validateBuyerOwnership,
  validateOrderCreationParams,
  validatePurchaseParams,
} from "../../services/auth-middleware";
import { createOrder } from "../../services/order-creation-service";
import { processPurchase } from "../../services/purchase-flow-service";
import { validateTelegramIdForOrderOperation } from "../../services/telegram-validation-service";

export interface CreateOrderAsBuyerParams {
  buyerId: string;
  collectionId: string;
  price: number;
}

export interface MakePurchaseAsBuyerParams {
  buyerId: string;
  orderId: string;
}

export async function validateCreateOrderRequest(
  request: any,
  params: CreateOrderAsBuyerParams
) {
  console.time("validateCreateOrderRequest");

  console.time("validateCreateOrderRequest-auth");
  const authRequest = requireAuthentication(request);
  validateOrderCreationParams(params, UserType.BUYER);
  validateBuyerOwnership(authRequest, params.buyerId);
  console.timeEnd("validateCreateOrderRequest-auth");

  console.time("validateCreateOrderRequest-userData");
  const user = await getUserData(authRequest.auth.uid);
  console.timeEnd("validateCreateOrderRequest-userData");

  console.time("validateCreateOrderRequest-telegramValidation");
  validateTelegramIdForOrderOperation(user, "create orders");
  console.timeEnd("validateCreateOrderRequest-telegramValidation");

  console.timeEnd("validateCreateOrderRequest");
  return { authRequest, user };
}

export async function validatePurchaseRequest(
  request: any,
  params: MakePurchaseAsBuyerParams
) {
  console.time("validatePurchaseRequest");

  console.time("validatePurchaseRequest-auth");
  const authRequest = requireAuthentication(request);
  validatePurchaseParams(params, UserType.BUYER);
  validateBuyerOwnership(authRequest, params.buyerId);
  console.timeEnd("validatePurchaseRequest-auth");

  console.time("validatePurchaseRequest-userData");
  // Validate user has telegram ID for purchase operations
  const user = await getUserData(authRequest.auth.uid);
  console.timeEnd("validatePurchaseRequest-userData");

  console.time("validatePurchaseRequest-telegramValidation");
  validateTelegramIdForOrderOperation(user, "make purchases");
  console.timeEnd("validatePurchaseRequest-telegramValidation");

  console.timeEnd("validatePurchaseRequest");
  return { authRequest, user };
}

export async function createBuyerOrder(params: CreateOrderAsBuyerParams) {
  console.time("createBuyerOrder");
  const { buyerId, collectionId, price } = params;

  const result = await createOrder({
    userId: buyerId,
    collectionId,
    price,
    giftId: null,
    userType: UserType.BUYER,
    secondaryMarketPrice: null,
  });

  console.timeEnd("createBuyerOrder");
  return result;
}

export async function processBuyerPurchase(params: MakePurchaseAsBuyerParams) {
  console.time("processBuyerPurchase");
  const { buyerId, orderId } = params;

  console.time("processBuyerPurchase-refundProposals");
  // Run proposal refund and purchase processing in parallel where possible
  // Note: refundAllActiveProposals must complete before processPurchase for data consistency
  await refundAllActiveProposals(orderId);
  console.timeEnd("processBuyerPurchase-refundProposals");

  console.time("processBuyerPurchase-processPurchase");
  const result = await processPurchase({
    userId: buyerId,
    orderId,
    userType: UserType.BUYER,
  });
  console.timeEnd("processBuyerPurchase-processPurchase");

  console.timeEnd("processBuyerPurchase");
  return result;
}
