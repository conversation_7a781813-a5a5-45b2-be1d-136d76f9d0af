import { onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig, LogOperations } from "../../constants";
import { BuyerOrderLogger } from "./buyer-order-function.logger";
import { BuyerOrderFunctionErrorHandler } from "./buyer-order-function.error-handler";
import {
  createBuyerOrder,
  validatePurchaseRequest,
  processBuyerPurchase,
  validateCreateOrderRequest,
} from "./buyer-order-function.service";

export const createOrderAsBuyer = onCall<{
  buyerId: string;
  collectionId: string;
  price: number;
}>(commonFunctionsConfig, async (request) => {
  const { buyerId, collectionId, price } = request.data;

  console.time("createOrderAsBuyer-total");
  try {
    // Validation now returns user data to avoid redundant database calls
    console.time("createOrderAsBuyer-validation");
    await validateCreateOrderRequest(request, {
      buyerId,
      collectionId,
      price,
    });
    console.timeEnd("createOrderAsBuyer-validation");

    console.time("createOrderAsBuyer-creation");
    const result = await createBuyerOrder({
      buyerId,
      collectionId,
      price,
    });
    console.timeEnd("createOrderAsBuyer-creation");

    console.timeEnd("createOrderAsBuyer-total");
    return result;
  } catch (error) {
    console.timeEnd("createOrderAsBuyer-total");
    BuyerOrderLogger.logCreateOrderError({
      error,
      operation: LogOperations.CREATE_ORDER_AS_BUYER,
      requestData: request.data,
      userId: request.auth?.uid,
    });

    BuyerOrderFunctionErrorHandler.throwCreateOrderError(
      (error as any).message
    );
  }
});

export const makePurchaseAsBuyer = onCall<{
  buyerId: string;
  orderId: string;
}>(commonFunctionsConfig, async (request) => {
  const { buyerId, orderId } = request.data;

  console.time("makePurchaseAsBuyer-total");
  try {
    // Validation now returns user data to avoid redundant database calls
    console.time("makePurchaseAsBuyer-validation");
    await validatePurchaseRequest(request, {
      buyerId,
      orderId,
    });
    console.timeEnd("makePurchaseAsBuyer-validation");

    console.time("makePurchaseAsBuyer-processing");
    const result = await processBuyerPurchase({
      buyerId,
      orderId,
    });
    console.timeEnd("makePurchaseAsBuyer-processing");

    console.timeEnd("makePurchaseAsBuyer-total");
    return result;
  } catch (error) {
    console.timeEnd("makePurchaseAsBuyer-total");
    BuyerOrderLogger.logPurchaseError({
      error,
      buyerId,
      orderId,
      operation: LogOperations.BUYER_PURCHASE,
    });

    BuyerOrderFunctionErrorHandler.throwPurchaseError((error as any).message);
  }
});
