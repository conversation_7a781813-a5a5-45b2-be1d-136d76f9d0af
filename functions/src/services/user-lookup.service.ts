import { UserEntity } from "../miker<PERSON>nko/marketplace-shared";
import { DBUserCollection } from "./db.service";

export interface UserLookupParams {
  userId?: string;
  tgId?: string;
}

export interface UserLookupResult {
  success: boolean;
  userId?: string;
  message?: string;
}

export async function getUserById(userId: string) {
  const userDoc = await DBUserCollection.doc(userId).get();

  if (!userDoc.exists) {
    return null;
  }

  return { id: userDoc.id, ...userDoc.data() } as UserEntity;
}
