import * as admin from "firebase-admin";

import { OrderEntity } from "../../mikerudenko/marketplace-shared";
import { DBOrdersCollection } from "../db.service";

export async function getOrderById(
  orderId: string
): Promise<OrderEntity | null> {
  const orderDoc = await DBOrdersCollection.doc(orderId).get();
  return orderDoc.exists
    ? ({ id: orderDoc.id, ...orderDoc.data() } as OrderEntity)
    : null;
}

export async function updateOrder(
  orderId: string,
  updates: Partial<OrderEntity>
): Promise<void> {
  await DBOrdersCollection.doc(orderId).update({
    ...updates,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });
}

/**
 * Calculate seller collateral adjustment when order price changes
 * Returns the difference that should be refunded to seller
 */
export function calculateSellerCollateralAdjustment(
  oldPrice: number,
  newPrice: number,
  sellerLockPercentageBPS: number
): { oldCollateral: number; newCollateral: number; refundAmount: number } {
  const sellerLockPercentage = sellerLockPercentageBPS / 10000; // Convert BPS to decimal

  const oldCollateral = oldPrice * sellerLockPercentage;
  const newCollateral = newPrice * sellerLockPercentage;
  const refundAmount = Math.max(0, oldCollateral - newCollateral);

  return {
    oldCollateral,
    newCollateral,
    refundAmount,
  };
}
