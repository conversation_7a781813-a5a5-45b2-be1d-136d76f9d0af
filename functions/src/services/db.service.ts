import * as admin from "firebase-admin";
import {
  APP_CONFIG_COLLECTION,
  APP_CONFIG_DOC_ID,
  APP_USERS_COLLECTION,
  AppConfigEntity,
  COLLECTION_NAME,
  CollectionEntity,
  COUNTERS_COLLECTION_NAME,
  GiftEntity,
  GIFTS_COLLECTION_NAME,
  OrderEntity,
  ORDERS_COLLECTION_NAME,
  RESELL_TX_HISTORY_COLLECTION_NAME,
  ResellTxHistoryEntity,
  TX_LOOKUP_COLLECTION_NAME,
  TX_LOOKUP_DOC_ID,
  TxLookupEntity,
  UserEntity,
} from "../mikerudenko/marketplace-shared";

export const db = admin.firestore();

export const DBUserCollection = db.collection(
  APP_USERS_COLLECTION
) as admin.firestore.CollectionReference<UserEntity>;

export const DBOrdersCollection = db.collection(
  ORDERS_COLLECTION_NAME
) as admin.firestore.CollectionReference<OrderEntity>;

export const DBGiftsCollection = db.collection(
  GIFTS_COLLECTION_NAME
) as admin.firestore.CollectionReference<GiftEntity>;

export const DBResellTxHistoryCollection = db.collection(
  RESELL_TX_HISTORY_COLLECTION_NAME
) as admin.firestore.CollectionReference<ResellTxHistoryEntity>;

export const DBTxLookupCollection = db.collection(
  TX_LOOKUP_COLLECTION_NAME
) as admin.firestore.CollectionReference<TxLookupEntity>;

export const DBTxLookupDoc = DBTxLookupCollection.doc(TX_LOOKUP_DOC_ID);

export const DBCollectionsCollection = db.collection(
  COLLECTION_NAME
) as admin.firestore.CollectionReference<CollectionEntity>;

export const DBAppConfigCollection = db.collection(
  APP_CONFIG_COLLECTION
) as admin.firestore.CollectionReference<AppConfigEntity>;

export const DBAppConfigDoc = DBAppConfigCollection.doc(APP_CONFIG_DOC_ID);

export const DBCountersCollection = db.collection(COUNTERS_COLLECTION_NAME);
