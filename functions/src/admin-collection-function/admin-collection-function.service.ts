import * as admin from "firebase-admin";
import {
  db,
  DBCollectionsCollection,
  DBOrdersCollection,
  DBUserCollection,
} from "../services/db.service";
import {
  CollectionEntity,
  OrderStatus,
  Role,
} from "../mikerudenko/marketplace-shared";
import { calculateOrderDeadline } from "../services/deadline-service/deadline-service";
import {
  throwCollectionNotFound,
  throwInvalidCollectionId,
  throwPermissionDenied,
} from "./admin-collection-function.error-handler";

export interface RecalculateDeadlinesParams {
  collectionId: string;
}

export interface ClearDeadlinesParams {
  collectionId: string;
}

export async function validateAdminUser(uid: string): Promise<void> {
  const userDoc = await DBUserCollection.doc(uid).get();
  if (!userDoc.exists || userDoc.data()?.role !== Role.ADMIN) {
    throwPermissionDenied();
  }
}

export async function validateCollectionExists(collectionId: string) {
  if (!collectionId) {
    throwInvalidCollectionId();
  }

  const collectionDoc = await DBCollectionsCollection.doc(collectionId).get();
  if (!collectionDoc.exists) {
    throwCollectionNotFound();
  }

  return { id: collectionDoc.id, ...collectionDoc.data() } as CollectionEntity;
}

export async function batchUpdateOrders(
  orders: admin.firestore.QueryDocumentSnapshot[],
  updateFields: Record<string, any>
): Promise<number> {
  const BATCH_SIZE = 400;
  let totalUpdatedCount = 0;

  for (let i = 0; i < orders.length; i += BATCH_SIZE) {
    const chunk = orders.slice(i, i + BATCH_SIZE);
    const batch = db.batch();

    for (const orderDoc of chunk) {
      batch.update(orderDoc.ref, {
        ...updateFields,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
    }

    await batch.commit();
    totalUpdatedCount += chunk.length;
  }

  return totalUpdatedCount;
}

export async function recalculateOrderDeadlines(collectionId: string) {
  const collection = await validateCollectionExists(collectionId);

  const ordersQuery = await DBOrdersCollection.where(
    "collectionId",
    "==",
    collectionId
  )
    .where("status", "in", [OrderStatus.ACTIVE, OrderStatus.PAID])
    .get();

  if (ordersQuery.empty) {
    return 0;
  }

  const orders = ordersQuery.docs;
  const updatePromises = orders.map(async (orderDoc) => {
    const newDeadline = await calculateOrderDeadline(collection);

    return {
      ref: orderDoc.ref,
      updateData: { deadline: newDeadline },
    };
  });

  const updates = await Promise.all(updatePromises);

  return batchUpdateOrders(orders, {
    deadline: updates[0]?.updateData.deadline,
  });
}

export async function clearOrderDeadlines(collectionId: string) {
  await validateCollectionExists(collectionId);

  const ordersQuery = await DBOrdersCollection.where(
    "collectionId",
    "==",
    collectionId
  )
    .where("status", "in", [OrderStatus.ACTIVE, OrderStatus.PAID])
    .get();

  if (ordersQuery.empty) {
    return 0;
  }

  return batchUpdateOrders(ordersQuery.docs, {
    deadline: admin.firestore.FieldValue.delete(),
  });
}
